# Kaggle Image Vector Sync

This standalone script replicates the `sync_image_vector` functionality for running on Kaggle. It extracts features from images stored in MongoDB and saves them to a Milvus vector database.

## Files

- `kaggle_sync_image_vector.py` - Main script containing all functionality
- `kaggle_requirements.txt` - Required Python packages
- `kaggle_setup.py` - Setup script for Kaggle environment
- `KAGGLE_README.md` - This documentation

## What it does

1. **Connects to MongoDB** to fetch image URLs that need feature extraction
2. **Connects to Milvus** vector database to check existing vectors and save new ones
3. **Processes images in batches** to efficiently handle large datasets
4. **Extracts features** using MobileNetV3 model from TensorFlow Hub
5. **Saves vectors** to Milvus, avoiding duplicates

## Setup Instructions

### 1. Upload Files to Kaggle

Upload these files to your Kaggle notebook:
- `kaggle_sync_image_vector.py`
- `kaggle_requirements.txt`
- `kaggle_setup.py`

### 2. Install Dependencies

Run the setup script:
```python
!python kaggle_setup.py
```

Or install manually:
```python
!pip install -r kaggle_requirements.txt
```

### 3. Set Environment Variables

In your Kaggle notebook, set the required environment variables:

```python
import os

# MongoDB connection
os.environ['MONGODB_URI'] = 'mongodb+srv://username:<EMAIL>/?retryWrites=true&w=majority'

# Milvus connection  
os.environ['MILVUS_URI'] = 'https://your-milvus-endpoint.com'
os.environ['MILVUS_TOKEN'] = 'your_milvus_token'

# Optional: Collection name (default: 'photos')
os.environ['VECTOR_COLLECTION_NAME'] = 'photos'
```

### 4. Run the Script

```python
!python kaggle_sync_image_vector.py
```

## Configuration

### Environment Variables

| Variable | Required | Default | Description |
|----------|----------|---------|-------------|
| `MONGODB_URI` | ✅ | - | MongoDB connection string |
| `MILVUS_URI` | ✅ | - | Milvus server URI |
| `MILVUS_TOKEN` | ✅ | - | Milvus authentication token |
| `VECTOR_COLLECTION_NAME` | ❌ | `photos` | Milvus collection name |

### Batch Processing

The script processes images in batches of 10 by default. You can modify the `batch_size` variable in the `sync_image_vector()` function if needed.

## Features

### Robust Error Handling
- Handles network timeouts and connection errors
- Falls back to individual processing if batch processing fails
- Continues processing even if some images fail

### Efficient Processing
- Checks existing vectors to avoid duplicates
- Batch feature extraction for better performance
- Progress logging for monitoring

### Memory Management
- Processes images in small batches to avoid memory issues
- Proper cleanup of resources

## Expected Output

```
2024-01-01 10:00:00 - root - INFO - Starting sync_image_vector process...
2024-01-01 10:00:01 - root - INFO - Connecting to MongoDB...
2024-01-01 10:00:02 - root - INFO - Successfully connected to MongoDB
2024-01-01 10:00:03 - root - INFO - Initializing Milvus connection...
2024-01-01 10:00:04 - root - INFO - Connected to Milvus, collection: photos
2024-01-01 10:00:05 - root - INFO - Loading MobileNetV3 model...
2024-01-01 10:00:15 - root - INFO - Model loaded in 10.23 seconds
2024-01-01 10:00:16 - root - INFO - Processing batch 1: 10 images
2024-01-01 10:00:17 - root - INFO - Found 8 images not in vector db out of 10 total
2024-01-01 10:00:18 - root - INFO - Extracting features for 8 missing images...
2024-01-01 10:00:25 - root - INFO - Successfully extracted features for 8 out of 8 images
2024-01-01 10:00:26 - root - INFO - Inserting 8 new vectors to Milvus
2024-01-01 10:00:27 - root - INFO - Vectors saved successfully
2024-01-01 10:00:28 - root - INFO - Progress: processed 10 images, synced 8 new vectors
...
2024-01-01 10:15:30 - root - INFO - sync_image_vector completed. Total processed: 1000, Total synced: 850
```

## Troubleshooting

### Common Issues

1. **Connection Errors**
   - Verify your MongoDB URI and Milvus credentials
   - Check if your Kaggle notebook has internet access

2. **Memory Issues**
   - Reduce batch size in the script
   - Restart the Kaggle kernel

3. **Model Loading Errors**
   - Ensure TensorFlow Hub can download the model
   - Check internet connectivity

### Performance Tips

1. **Use GPU acceleration** if available in Kaggle
2. **Monitor memory usage** and adjust batch size accordingly
3. **Run during off-peak hours** for better network performance

## Differences from Original

This standalone version includes all dependencies inline and:
- Uses direct MongoDB and Milvus connections instead of dependency injection
- Includes all utility functions in a single file
- Simplified error handling for Kaggle environment
- Added environment variable validation

## Support

If you encounter issues:
1. Check the logs for specific error messages
2. Verify all environment variables are set correctly
3. Ensure your databases are accessible from Kaggle's network
