#!/usr/bin/env python3
"""
Standalone script for Kaggle to sync image vectors.
This script replicates the sync_image_vector functionality from the main application.

Process:
1. Connect to MongoDB and Milvus
2. Get images from MongoDB in batches
3. Check which images are not in vector db
4. Extract features for missing images and insert to vector db

Required environment variables:
- MONGODB_URI: MongoDB connection string
- MILVUS_URI: Milvus connection URI
- MILVUS_TOKEN: Mil<PERSON>s authentication token
- VECTOR_COLLECTION_NAME: Milvus collection name (default: 'photos')
"""

import os
import logging
import time
import numpy as np
from typing import List, Optional, Union
import requests
from PIL import Image
import io

# TensorFlow and TensorFlow Hub for feature extraction
import tensorflow as tf
import tensorflow_hub as hub

# MongoDB
import pymongo
from pymongo.mongo_client import MongoClient
from pymongo.server_api import ServerApi

# Milvus
from pymilvus import MilvusClient

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)

class ImageLoader:
    """Utility class for loading images from URLs"""
    
    @staticmethod
    def load_image_url(img_url, required_size=(224, 224)):
        """Load image from URL and preprocess for feature extraction"""
        try:
            response = requests.get(img_url, stream=True, timeout=10)
            if response.status_code != 200:
                return {'error': f'Failed to fetch image. Status code: {response.status_code}'}
                
            content_type = response.headers.get('content-type', '')
            if not content_type.startswith('image/'):
                return {'error': f'URL does not point to an image. Content-Type: {content_type}'}

            img = Image.open(response.raw)
            
            # Convert to RGB if necessary
            if img.mode not in ['RGB', 'L']:
                img = img.convert('RGB')
            if img.mode == 'L':
                img = img.convert('RGB')
                
            # Resize image
            img = img.resize(required_size)
            
            # Convert to numpy array and normalize
            img_array = np.array(img)
            img_array = np.expand_dims(img_array, 0) / 255.0
            img_array = img_array.astype(np.float32)
            
            return img_array
            
        except Exception as e:
            logging.error(f'Error loading image from {img_url}: {str(e)}')
            return {'error': str(e)}

class FeatureExtractionService:
    """Feature extraction using MobileNetV3"""
    
    def __init__(self):
        logging.info('Loading MobileNetV3 model...')
        start = time.time()
        module_handle = "https://tfhub.dev/google/imagenet/mobilenet_v3_large_100_224/feature_vector/5"
        self.module = hub.load(module_handle)
        logging.info(f"Model loaded in {time.time() - start:.2f} seconds")

    def extract_features(self, image: Optional[np.ndarray] = None, 
                        image_url: Optional[str] = None, 
                        image_urls: Optional[List[str]] = None) -> Union[np.ndarray, List[np.ndarray]]:
        """Extract features from image(s)"""
        try:
            # Validate input
            provided_params = sum([image is not None, image_url is not None, image_urls is not None])
            if provided_params == 0:
                raise ValueError("One of image, image_url, or image_urls must be provided")
            if provided_params > 1:
                raise ValueError("Only one of image, image_url, or image_urls should be provided")

            # Handle batch processing
            if image_urls is not None:
                return self._extract_features_batch(image_urls)

            # Handle single image
            if image is None and image_url is not None:
                image = ImageLoader.load_image_url(image_url)
                if isinstance(image, dict) and 'error' in image:
                    raise ValueError(f"Failed to load image from URL: {image['error']}")
                if len(image) == 0:
                    raise ValueError(f"Failed to load image from URL: {image_url}")

            # Extract features
            feature = self.module(image)
            return np.squeeze(feature)
            
        except Exception as e:
            logging.error(f'Error extracting features: {e}')
            return np.array([])

    def _extract_features_batch(self, image_urls: List[str]) -> List[np.ndarray]:
        """Extract features from multiple image URLs"""
        results = []
        for i, url in enumerate(image_urls):
            try:
                image = ImageLoader.load_image_url(url)
                if isinstance(image, dict) and 'error' in image:
                    logging.warning(f"Failed to load image at index {i}: {url}, error: {image['error']}")
                    results.append(np.array([]))
                    continue

                if len(image) == 0:
                    logging.warning(f"Empty image at index {i}: {url}")
                    results.append(np.array([]))
                    continue

                feature = self.module(image)
                results.append(np.squeeze(feature))

            except Exception as e:
                logging.warning(f"Failed to extract features for image at index {i}: {url}, error: {e}")
                results.append(np.array([]))
                continue

        return results

class MilvusVectorDB:
    """Milvus vector database operations"""
    
    def __init__(self):
        logging.info('Initializing Milvus connection...')
        self.client = MilvusClient(
            uri=os.environ.get('MILVUS_URI'),
            token=os.environ.get('MILVUS_TOKEN')
        )
        
        self.collection_name = os.environ.get('VECTOR_COLLECTION_NAME', 'photos')
        logging.info(f"Connected to Milvus, collection: {self.collection_name}")

        # Check if collection exists
        if not self.client.has_collection(self.collection_name):
            logging.error(f"Collection {self.collection_name} does not exist!")
            raise ValueError(f"Collection {self.collection_name} not found")
        else:
            logging.info(f'Collection {self.collection_name} exists')

    def save(self, vector_data):
        """Save vectors to Milvus"""
        logging.info(f'Saving {len(vector_data)} vectors to Milvus...')
        
        # Check which URLs already exist
        urls = [v['url'] for v in vector_data]
        existing_urls = self.fetch_by_url(urls)
        existing_urls_set = set(existing_urls)
        
        # Filter out existing URLs
        new_data = [v for v in vector_data if v['url'] not in existing_urls_set]
        
        if len(new_data) == 0:
            logging.info('No new vectors to save')
            return

        # Prepare data for insertion
        rows = []
        for item in new_data:
            rows.append({
                'url': item['url'],
                'feature': item['vector']
            })

        logging.info(f'Inserting {len(rows)} new vectors to Milvus')
        self.client.insert(
            collection_name=self.collection_name,
            data=rows
        )
        self.client.flush(self.collection_name)
        logging.info('Vectors saved successfully')

    def fetch_by_url(self, urls):
        """Check which URLs exist in vector database"""
        if not urls:
            return []
            
        link_in = "','".join(urls)
        res = self.client.query(
            collection_name=self.collection_name,
            filter=f"url in ['{link_in}']",
            output_fields=["url"]
        )
        
        return [row['url'] for row in res]

class MongoDBConnection:
    """MongoDB connection and operations"""
    
    def __init__(self):
        uri = os.environ.get('MONGODB_URI')
        if not uri:
            raise ValueError("MONGODB_URI environment variable is required")
        
        logging.info('Connecting to MongoDB...')
        self.client = MongoClient(uri, server_api=ServerApi('1'))
        
        # Test connection
        try:
            self.client.admin.command('ping')
            logging.info("Successfully connected to MongoDB")
        except Exception as e:
            logging.error(f"Failed to connect to MongoDB: {e}")
            raise
            
        self.db = self.client['similarity']
        self.images_collection = self.db['images']

    def get_images_batch(self, skip, limit):
        """Get batch of images from MongoDB"""
        return list(self.images_collection.find({}, {"url": 1}).skip(skip).limit(limit))

def ensure_float32(vector):
    """Ensure vector is float32 for Milvus compatibility"""
    if isinstance(vector, np.ndarray):
        return vector.astype(np.float32)
    return np.array(vector, dtype=np.float32)

def sync_image_vector():
    """
    Main function to sync image vectors.

    Process:
    1. Get images from MongoDB in batches
    2. Check which images are not in vector db using fetch_by_url
    3. Extract features for missing images and insert to vector db
    """
    logging.info('Starting sync_image_vector process...')

    # Initialize services
    mongo_db = MongoDBConnection()
    vector_db = MilvusVectorDB()
    feature_service = FeatureExtractionService()

    batch_size = 10
    skip = 0
    total_processed = 0
    total_synced = 0

    try:
        while True:
            # Get batch of images from MongoDB
            images_batch = mongo_db.get_images_batch(skip, batch_size)

            if not images_batch:
                logging.info(f"No more images to process. Total processed: {total_processed}, Total synced: {total_synced}")
                break

            logging.info(f"Processing batch {skip//batch_size + 1}: {len(images_batch)} images")

            # Extract URLs from the batch
            urls = [img["url"] for img in images_batch]

            # Check which URLs are already in vector db
            existing_urls = vector_db.fetch_by_url(urls)
            existing_urls_set = set(existing_urls)

            # Find URLs that are not in vector db
            missing_urls = [url for url in urls if url not in existing_urls_set]

            logging.info(f"Found {len(missing_urls)} images not in vector db out of {len(urls)} total")

            if missing_urls:
                # Extract features for all missing images in batch
                logging.info(f"Extracting features for {len(missing_urls)} missing images...")

                try:
                    # Use batch feature extraction
                    features_batch = feature_service.extract_features(image_urls=missing_urls)

                    # Prepare vector data
                    vector_data = []
                    for i, (url, feature) in enumerate(zip(missing_urls, features_batch)):
                        if feature is not None and len(feature) > 0:
                            # Ensure feature is float32 for Milvus
                            feature = ensure_float32(feature)
                            vector_data.append({'url': url, 'vector': feature})
                        else:
                            logging.warning(f"Failed to extract features for image: {url}")

                    logging.info(f"Successfully extracted features for {len(vector_data)} out of {len(missing_urls)} images")

                except Exception as e:
                    logging.error(f"Error in batch feature extraction: {str(e)}")
                    # Fallback to individual processing
                    logging.info("Falling back to individual feature extraction...")
                    vector_data = []

                    for i, url in enumerate(missing_urls):
                        try:
                            logging.debug(f"Extracting features for image {i+1}/{len(missing_urls)}: {url}")

                            # Extract image feature using feature service
                            img_feature = feature_service.extract_features(image_url=url)

                            if img_feature is None or len(img_feature) == 0:
                                logging.warning(f"Failed to extract features for image: {url}")
                                continue

                            # Ensure feature is float32 for Milvus
                            img_feature = ensure_float32(img_feature)
                            vector_data.append({'url': url, 'vector': img_feature})

                        except Exception as e:
                            logging.error(f"Error processing image {url}: {str(e)}")
                            continue

                # Insert to vector db if we have valid features
                if vector_data:
                    try:
                        vector_db.save(vector_data)
                        total_synced += len(vector_data)
                        logging.info(f"Successfully synced {len(vector_data)} image vectors to vector db")
                    except Exception as e:
                        logging.error(f"Error saving vectors to db: {str(e)}")
                        raise

            total_processed += len(images_batch)
            skip += batch_size

            # Log progress
            logging.info(f"Progress: processed {total_processed} images, synced {total_synced} new vectors")

    except Exception as e:
        logging.error(f"Error in sync_image_vector: {str(e)}")
        raise

    logging.info(f"sync_image_vector completed. Total processed: {total_processed}, Total synced: {total_synced}")

def main():
    """Main execution function"""
    logging.info("Starting Kaggle Image Vector Sync Job")

    # Check required environment variables
    required_env_vars = ['MONGODB_URI', 'MILVUS_URI', 'MILVUS_TOKEN']
    missing_vars = [var for var in required_env_vars if not os.environ.get(var)]

    if missing_vars:
        logging.error(f"Missing required environment variables: {missing_vars}")
        return 1

    try:
        sync_image_vector()
        logging.info("Job completed successfully!")
        return 0
    except Exception as e:
        logging.error(f"Job failed with error: {str(e)}")
        return 1

if __name__ == "__main__":
    exit_code = main()
