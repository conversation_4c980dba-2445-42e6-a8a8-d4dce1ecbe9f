from fastapi import <PERSON><PERSON><PERSON><PERSON>, <PERSON>pen<PERSON>, <PERSON>er, Query, HTTPException
from typing import Optional, Any
from app.services.clustering_service import ClusteringService
from app.schemas.clustering import ClusterResponse, ImageClusterResponse, SortOrder
from app.dependencies.dependencies import get_clustering_api_service
import logging

router = APIRouter(prefix="/clusters", tags=["clusters"])





@router.get("/", response_model=ClusterResponse)
def get_clusters(
    min_photos: Optional[int] = Query(None, ge=1, description="Minimum number of photos per cluster"),
    max_photos: Optional[int] = Query(None, ge=1, description="Maximum number of photos per cluster"),
    sort_by: str = Query("total_photos", description="Sort by field (total_photos)"),
    sort_order: SortOrder = Query(SortOrder.DESC, description="Sort order (asc/desc)"),
    limit: Optional[int] = Query(None, ge=1, le=1000, description="Maximum number of clusters to return"),
    public_key: str = Header(),
    clustering_service: ClusteringService = Depends(get_clustering_api_service)
) -> Any:
    """
    Get cluster data grouped by cluster_id.
    
    This endpoint returns all cluster data grouped by cluster ID. Each cluster contains
    a list of images that belong to that cluster.
    
    **Available Parameters:**
    - **min_photos**: Filter clusters that have at least this many photos
    - **max_photos**: Filter clusters that have at most this many photos  
    - **sort_by**: Sort clusters by field (currently supports 'total_photos')
    - **sort_order**: Sort order - 'asc' for ascending, 'desc' for descending
    - **limit**: Return only top N clusters (useful with sorting)
    
    **Example Usage:**
    - Get all clusters: `GET /v1/clusters/`
    - Get clusters with at least 4 photos: `GET /v1/clusters/?min_photos=4`
    - Get top 10 clusters by photo count: `GET /v1/clusters/?sort_by=total_photos&sort_order=desc&limit=10`
    """
    try:
        # Validate min_photos and max_photos relationship
        if min_photos is not None and max_photos is not None and min_photos > max_photos:
            raise HTTPException(
                status_code=400,
                detail="min_photos cannot be greater than max_photos"
            )
        
        result = clustering_service.get_clusters(
            min_photos=min_photos,
            max_photos=max_photos,
            sort_by=sort_by,
            sort_order=sort_order.value,
            limit=limit
        )
        
        if result.error:
            raise HTTPException(status_code=500, detail=result.message)
        
        return result
        
    except HTTPException:
        raise
    except Exception as e:
        logging.error(f"Error in get_clusters endpoint: {str(e)}")
        raise HTTPException(
            status_code=500,
            detail=f"Internal server error: {str(e)}"
        )


@router.get("/find-by-image", response_model=ImageClusterResponse)
def find_cluster_by_image(
    image_url: str = Query(..., description="URL of the image to find cluster for"),
    public_key: str = Header(),
    clustering_service: ClusteringService = Depends(get_clustering_api_service)
) -> Any:
    """
    Find which cluster a specific image belongs to.
    
    This endpoint takes an image URL and returns information about which cluster
    the image belongs to, along with all other images in that cluster.
    
    **Parameters:**
    - **image_url**: The URL of the image you want to find the cluster for
    
    **Returns:**
    - cluster_id: The ID of the cluster the image belongs to
    - cluster_data: Complete data for the cluster including all images
    
    **Example Usage:**
    - Find cluster for an image: `GET /v1/clusters/find-by-image?image_url=https://example.com/photo.jpg`
    """
    try:
        if not image_url.strip():
            raise HTTPException(
                status_code=400,
                detail="image_url parameter cannot be empty"
            )
        
        result = clustering_service.find_image_cluster(image_url)
        
        if result.error:
            raise HTTPException(status_code=500, detail=result.message)
        
        return result
        
    except HTTPException:
        raise
    except Exception as e:
        logging.error(f"Error in find_cluster_by_image endpoint: {str(e)}")
        raise HTTPException(
            status_code=500,
            detail=f"Internal server error: {str(e)}"
        )
