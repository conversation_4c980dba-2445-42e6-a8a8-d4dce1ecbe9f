from typing import List, Optional, Dict, Any
from domain.repositories.similarity_repository import SimilarityRepository
from app.schemas.clustering import ClusterData, ClusterImage, ClusterResponse, ImageClusterResponse
import logging


class ClusteringService:
    """Service for handling cluster-related operations"""
    
    def __init__(self, repo: SimilarityRepository):
        self.repo = repo
        logging.info('ClusteringService initialized')

    def get_clusters(self, min_photos: Optional[int] = None, max_photos: Optional[int] = None,
                    sort_by: str = "total_photos", sort_order: str = "desc",
                    limit: Optional[int] = None, max_images_per_cluster: Optional[int] = 100) -> ClusterResponse:
        """
        Get cluster data with optional filtering and sorting.
        
        Args:
            min_photos: Minimum number of photos per cluster
            max_photos: Maximum number of photos per cluster
            sort_by: Field to sort by (default: total_photos)
            sort_order: Sort order - 'asc' or 'desc' (default: desc)
            limit: Maximum number of clusters to return
            
        Returns:
            ClusterResponse with cluster data
        """
        try:
            # Fetch clusters from repository
            clusters_data = self.repo.fetch_clusters(
                min_photos=min_photos,
                max_photos=max_photos,
                sort_by=sort_by,
                sort_order=sort_order,
                limit=limit,
                max_images_per_cluster=max_images_per_cluster
            )
            
            # Convert to response format
            clusters = []
            for cluster_data in clusters_data:
                # Convert images to ClusterImage objects
                images = []
                for img_data in cluster_data["images"]:
                    image = ClusterImage(
                        url=img_data["url"],
                        title=img_data.get("title"),
                        category=img_data.get("category"),
                        id=img_data.get("id"),
                        event=img_data.get("event"),
                        user=img_data.get("user"),
                        meta_data=img_data.get("meta_data")
                    )
                    images.append(image)
                
                cluster = ClusterData(
                    cluster_id=cluster_data["cluster_id"],
                    images=images,
                    total_images=cluster_data["total_images"]
                )
                clusters.append(cluster)
            
            return ClusterResponse(
                message="success",
                result=clusters,
                total_clusters=len(clusters),
                error=False
            )
            
        except Exception as e:
            logging.error(f"Error fetching clusters: {str(e)}")
            return ClusterResponse(
                message=f"Error fetching clusters: {str(e)}",
                result=[],
                total_clusters=0,
                error=True
            )

    def find_image_cluster(self, image_url: str) -> ImageClusterResponse:
        """
        Find which cluster an image belongs to.
        
        Args:
            image_url: URL of the image to find
            
        Returns:
            ImageClusterResponse with cluster information
        """
        try:
            cluster_data = self.repo.find_image_cluster(image_url)
            
            if not cluster_data:
                return ImageClusterResponse(
                    message="Image not found or not assigned to any cluster",
                    cluster_id=None,
                    cluster_data=None,
                    error=False
                )
            
            # Convert images to ClusterImage objects
            images = []
            for img_data in cluster_data["images"]:
                image = ClusterImage(
                    url=img_data["url"],
                    title=img_data.get("title"),
                    category=img_data.get("category"),
                    id=img_data.get("id"),
                    event=img_data.get("event"),
                    user=img_data.get("user"),
                    meta_data=img_data.get("meta_data")
                )
                images.append(image)
            
            cluster = ClusterData(
                cluster_id=cluster_data["cluster_id"],
                images=images,
                total_images=cluster_data["total_images"]
            )
            
            return ImageClusterResponse(
                message="success",
                cluster_id=cluster_data["cluster_id"],
                cluster_data=cluster,
                error=False
            )
            
        except Exception as e:
            logging.error(f"Error finding image cluster: {str(e)}")
            return ImageClusterResponse(
                message=f"Error finding image cluster: {str(e)}",
                cluster_id=None,
                cluster_data=None,
                error=True
            )
