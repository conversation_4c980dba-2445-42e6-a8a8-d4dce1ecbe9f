import abc
from typing import Optional, List, Dict, Any
from domain.entities.photo import Photo

class SimilarityRepository(abc.ABC):
    """
    An abstract class for similarity repository.
    """

    @abc.abstractmethod
    def fetch_image_detail(self, url) -> Optional[Photo]:
        """
        Fetch photo information by url
        """
        raise NotImplementedError

    @abc.abstractmethod
    def fetch_image_by_index(self, index) -> Optional[str]:
        raise NotImplementedError

    @abc.abstractmethod
    async def batch_insert_photos(self, photos: List[Dict[str, Any]]) -> Dict[str, Any]:
        """
        Insert multiple photos in a batch operation.

        Args:
            photos: List of photo data dictionaries to insert

        Returns:
            Dict containing operation results and statistics
        """
        raise NotImplementedError

    @abc.abstractmethod
    def fetch_clusters(self, min_photos: Optional[int] = None, max_photos: Optional[int] = None,
                      sort_by: str = "total_photos", sort_order: str = "desc",
                      limit: Optional[int] = None) -> List[Dict[str, Any]]:
        """
        Fetch cluster data grouped by cluster_id.

        Args:
            min_photos: Minimum number of photos per cluster
            max_photos: Maximum number of photos per cluster
            sort_by: Field to sort by (default: total_photos)
            sort_order: Sort order - 'asc' or 'desc' (default: desc)
            limit: Maximum number of clusters to return

        Returns:
            List of cluster data dictionaries
        """
        raise NotImplementedError

    @abc.abstractmethod
    def find_image_cluster(self, image_url: str) -> Optional[Dict[str, Any]]:
        """
        Find which cluster an image belongs to.

        Args:
            image_url: URL of the image to find

        Returns:
            Dictionary containing cluster_id and cluster data, or None if not found
        """
        raise NotImplementedError
