#!/usr/bin/env python3
"""
Setup script for Kaggle environment.
Run this first to install dependencies and set up environment variables.
"""

import subprocess
import sys
import os

def install_requirements():
    """Install required packages"""
    print("Installing required packages...")
    try:
        subprocess.check_call([sys.executable, "-m", "pip", "install", "-r", "kaggle_requirements.txt"])
        print("✅ Dependencies installed successfully!")
    except subprocess.CalledProcessError as e:
        print(f"❌ Failed to install dependencies: {e}")
        return False
    return True

def setup_environment():
    """Setup environment variables"""
    print("\n🔧 Setting up environment variables...")
    print("Please set the following environment variables in your Kaggle notebook:")
    print()
    print("import os")
    print("# MongoDB connection")
    print("os.environ['MONGODB_URI'] = 'your_mongodb_connection_string'")
    print()
    print("# Milvus connection")
    print("os.environ['MILVUS_URI'] = 'your_milvus_uri'")
    print("os.environ['MILVUS_TOKEN'] = 'your_milvus_token'")
    print()
    print("# Optional: Collection name (default: 'photos')")
    print("os.environ['VECTOR_COLLECTION_NAME'] = 'photos'")
    print()

def main():
    print("🚀 Setting up Kaggle environment for Image Vector Sync")
    print("=" * 50)
    
    if not install_requirements():
        return 1
    
    setup_environment()
    
    print("\n✅ Setup complete!")
    print("\nNext steps:")
    print("1. Set the environment variables as shown above")
    print("2. Run: python kaggle_sync_image_vector.py")
    
    return 0

if __name__ == "__main__":
    exit(main())
