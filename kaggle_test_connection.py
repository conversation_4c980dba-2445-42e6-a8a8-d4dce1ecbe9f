#!/usr/bin/env python3
"""
Test script to verify connections to MongoDB and Milvus before running the main sync.
Run this first to ensure everything is configured correctly.
"""

import os
import logging
from pymongo.mongo_client import MongoClient
from pymongo.server_api import ServerApi
from pymilvus import MilvusClient

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s'
)

def test_mongodb_connection():
    """Test MongoDB connection"""
    print("🔍 Testing MongoDB connection...")
    
    uri = os.environ.get('MONGODB_URI')
    if not uri:
        print("❌ MONGODB_URI environment variable not set")
        return False
    
    try:
        client = MongoClient(uri, server_api=ServerApi('1'))
        client.admin.command('ping')
        
        # Test access to similarity database
        db = client['similarity']
        images_collection = db['images']
        
        # Count documents
        count = images_collection.count_documents({})
        print(f"✅ MongoDB connection successful!")
        print(f"   Database: similarity")
        print(f"   Images collection: {count} documents")
        
        # Test a small query
        sample = list(images_collection.find({}, {"url": 1}).limit(1))
        if sample:
            print(f"   Sample image URL: {sample[0].get('url', 'N/A')}")
        
        client.close()
        return True
        
    except Exception as e:
        print(f"❌ MongoDB connection failed: {str(e)}")
        return False

def test_milvus_connection():
    """Test Milvus connection"""
    print("\n🔍 Testing Milvus connection...")
    
    uri = os.environ.get('MILVUS_URI')
    token = os.environ.get('MILVUS_TOKEN')
    collection_name = os.environ.get('VECTOR_COLLECTION_NAME', 'photos')
    
    if not uri:
        print("❌ MILVUS_URI environment variable not set")
        return False
    
    if not token:
        print("❌ MILVUS_TOKEN environment variable not set")
        return False
    
    try:
        client = MilvusClient(uri=uri, token=token)
        
        # Check if collection exists
        if not client.has_collection(collection_name):
            print(f"❌ Collection '{collection_name}' does not exist")
            return False
        
        # Get collection info
        collection_info = client.describe_collection(collection_name)
        print(f"✅ Milvus connection successful!")
        print(f"   Collection: {collection_name}")
        print(f"   Collection exists: True")
        
        # Test a simple query to count documents
        try:
            result = client.query(
                collection_name=collection_name,
                filter="",
                output_fields=["url"],
                limit=1
            )
            print(f"   Sample query successful")
            if result:
                print(f"   Sample vector URL: {result[0].get('url', 'N/A')}")
        except Exception as e:
            print(f"   Warning: Could not run sample query: {str(e)}")
        
        return True
        
    except Exception as e:
        print(f"❌ Milvus connection failed: {str(e)}")
        return False

def test_feature_extraction():
    """Test feature extraction model loading"""
    print("\n🔍 Testing feature extraction model...")
    
    try:
        import tensorflow as tf
        import tensorflow_hub as hub
        import numpy as np
        
        print("   Loading MobileNetV3 model...")
        module_handle = "https://tfhub.dev/google/imagenet/mobilenet_v3_large_100_224/feature_vector/5"
        module = hub.load(module_handle)
        
        # Test with a dummy image
        dummy_image = np.random.rand(1, 224, 224, 3).astype(np.float32)
        features = module(dummy_image)
        
        print(f"✅ Feature extraction model loaded successfully!")
        print(f"   Model output shape: {features.shape}")
        print(f"   Feature vector dimension: {features.shape[-1]}")
        
        return True
        
    except Exception as e:
        print(f"❌ Feature extraction test failed: {str(e)}")
        return False

def main():
    """Run all connection tests"""
    print("🧪 Testing Kaggle Image Vector Sync Setup")
    print("=" * 50)
    
    # Check environment variables
    print("📋 Checking environment variables...")
    required_vars = ['MONGODB_URI', 'MILVUS_URI', 'MILVUS_TOKEN']
    missing_vars = []
    
    for var in required_vars:
        if os.environ.get(var):
            print(f"   ✅ {var}: Set")
        else:
            print(f"   ❌ {var}: Not set")
            missing_vars.append(var)
    
    optional_vars = ['VECTOR_COLLECTION_NAME']
    for var in optional_vars:
        value = os.environ.get(var, 'default')
        print(f"   ℹ️  {var}: {value}")
    
    if missing_vars:
        print(f"\n❌ Missing required environment variables: {missing_vars}")
        print("Please set these variables before running the sync script.")
        return 1
    
    print()
    
    # Run tests
    tests = [
        ("MongoDB", test_mongodb_connection),
        ("Milvus", test_milvus_connection),
        ("Feature Extraction", test_feature_extraction)
    ]
    
    results = []
    for test_name, test_func in tests:
        try:
            result = test_func()
            results.append((test_name, result))
        except Exception as e:
            print(f"❌ {test_name} test crashed: {str(e)}")
            results.append((test_name, False))
    
    # Summary
    print("\n" + "=" * 50)
    print("📊 Test Results Summary:")
    
    all_passed = True
    for test_name, passed in results:
        status = "✅ PASS" if passed else "❌ FAIL"
        print(f"   {test_name}: {status}")
        if not passed:
            all_passed = False
    
    if all_passed:
        print("\n🎉 All tests passed! You're ready to run the sync script.")
        print("Run: python kaggle_sync_image_vector.py")
        return 0
    else:
        print("\n⚠️  Some tests failed. Please fix the issues before running the sync script.")
        return 1

if __name__ == "__main__":
    exit(main())
