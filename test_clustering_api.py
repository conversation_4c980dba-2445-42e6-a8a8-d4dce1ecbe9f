#!/usr/bin/env python3
"""
Test script to demonstrate the clustering API functionality.
This script shows how the API endpoints would work with sample data.
"""

import json
from typing import List, Dict, Any

# Mock data to simulate MongoDB cluster data
MOCK_CLUSTER_DATA = [
    {
        "cluster_id": 1,
        "total_images": 5,
        "images": [
            {
                "url": "https://example.com/photo1.jpg",
                "title": "Sunset Beach",
                "category": "Nature",
                "id": "img_001",
                "event": {"year": "2024", "name": "Photo Contest", "short_name": "PC2024"},
                "user": {"name": "<PERSON>", "email": "<EMAIL>", "honors": "EFIAP", "club": "Photo Club", "fp_id": "fp001"},
                "meta_data": {"camera": "Canon EOS R5", "location": "Bali"}
            },
            {
                "url": "https://example.com/photo2.jpg",
                "title": "Ocean Waves",
                "category": "Nature",
                "id": "img_002",
                "event": {"year": "2024", "name": "Photo Contest", "short_name": "PC2024"},
                "user": {"name": "<PERSON>", "email": "<EMAIL>", "honors": "AFIAP", "club": "Nature Club", "fp_id": "fp002"},
                "meta_data": {"camera": "Nikon D850", "location": "Hawaii"}
            },
            {
                "url": "https://example.com/photo3.jpg",
                "title": "Golden Hour",
                "category": "Landscape",
                "id": "img_003",
                "event": {"year": "2024", "name": "Photo Contest", "short_name": "PC2024"},
                "user": {"name": "Bob Wilson", "email": "<EMAIL>", "honors": None, "club": "Landscape Club", "fp_id": "fp003"},
                "meta_data": {"camera": "Sony A7R IV", "location": "California"}
            },
            {
                "url": "https://example.com/photo4.jpg",
                "title": "Coastal View",
                "category": "Nature",
                "id": "img_004",
                "event": {"year": "2024", "name": "Photo Contest", "short_name": "PC2024"},
                "user": {"name": "Alice Brown", "email": "<EMAIL>", "honors": "EFIAP", "club": "Photo Club", "fp_id": "fp004"},
                "meta_data": {"camera": "Canon EOS R6", "location": "Oregon"}
            },
            {
                "url": "https://example.com/photo5.jpg",
                "title": "Seascape",
                "category": "Nature",
                "id": "img_005",
                "event": {"year": "2024", "name": "Photo Contest", "short_name": "PC2024"},
                "user": {"name": "Charlie Davis", "email": "<EMAIL>", "honors": "AFIAP", "club": "Nature Club", "fp_id": "fp005"},
                "meta_data": {"camera": "Fujifilm X-T4", "location": "Maine"}
            }
        ]
    },
    {
        "cluster_id": 2,
        "total_images": 3,
        "images": [
            {
                "url": "https://example.com/photo6.jpg",
                "title": "City Lights",
                "category": "Urban",
                "id": "img_006",
                "event": {"year": "2024", "name": "Photo Contest", "short_name": "PC2024"},
                "user": {"name": "David Lee", "email": "<EMAIL>", "honors": "EFIAP", "club": "Urban Club", "fp_id": "fp006"},
                "meta_data": {"camera": "Canon EOS 5D", "location": "New York"}
            },
            {
                "url": "https://example.com/photo7.jpg",
                "title": "Night Street",
                "category": "Urban",
                "id": "img_007",
                "event": {"year": "2024", "name": "Photo Contest", "short_name": "PC2024"},
                "user": {"name": "Eva Green", "email": "<EMAIL>", "honors": None, "club": "Street Club", "fp_id": "fp007"},
                "meta_data": {"camera": "Nikon Z6", "location": "Tokyo"}
            },
            {
                "url": "https://example.com/photo8.jpg",
                "title": "Urban Architecture",
                "category": "Architecture",
                "id": "img_008",
                "event": {"year": "2024", "name": "Photo Contest", "short_name": "PC2024"},
                "user": {"name": "Frank Miller", "email": "<EMAIL>", "honors": "AFIAP", "club": "Architecture Club", "fp_id": "fp008"},
                "meta_data": {"camera": "Sony A7 III", "location": "London"}
            }
        ]
    },
    {
        "cluster_id": 3,
        "total_images": 7,
        "images": [
            {
                "url": "https://example.com/photo9.jpg",
                "title": "Portrait 1",
                "category": "Portrait",
                "id": "img_009",
                "event": {"year": "2024", "name": "Photo Contest", "short_name": "PC2024"},
                "user": {"name": "Grace Taylor", "email": "<EMAIL>", "honors": "EFIAP", "club": "Portrait Club", "fp_id": "fp009"},
                "meta_data": {"camera": "Canon EOS R", "location": "Studio"}
            },
            {
                "url": "https://example.com/photo10.jpg",
                "title": "Portrait 2",
                "category": "Portrait",
                "id": "img_010",
                "event": {"year": "2024", "name": "Photo Contest", "short_name": "PC2024"},
                "user": {"name": "Henry White", "email": "<EMAIL>", "honors": None, "club": "Portrait Club", "fp_id": "fp010"},
                "meta_data": {"camera": "Nikon D780", "location": "Studio"}
            },
            # Adding more images to make this cluster have 7 total
            {
                "url": "https://example.com/photo11.jpg",
                "title": "Portrait 3",
                "category": "Portrait",
                "id": "img_011",
                "event": {"year": "2024", "name": "Photo Contest", "short_name": "PC2024"},
                "user": {"name": "Ivy Black", "email": "<EMAIL>", "honors": "AFIAP", "club": "Portrait Club", "fp_id": "fp011"},
                "meta_data": {"camera": "Sony A7R V", "location": "Outdoor"}
            },
            {
                "url": "https://example.com/photo12.jpg",
                "title": "Portrait 4",
                "category": "Portrait",
                "id": "img_012",
                "event": {"year": "2024", "name": "Photo Contest", "short_name": "PC2024"},
                "user": {"name": "Jack Gray", "email": "<EMAIL>", "honors": "EFIAP", "club": "Portrait Club", "fp_id": "fp012"},
                "meta_data": {"camera": "Canon EOS R5", "location": "Studio"}
            },
            {
                "url": "https://example.com/photo13.jpg",
                "title": "Portrait 5",
                "category": "Portrait",
                "id": "img_013",
                "event": {"year": "2024", "name": "Photo Contest", "short_name": "PC2024"},
                "user": {"name": "Kate Blue", "email": "<EMAIL>", "honors": None, "club": "Portrait Club", "fp_id": "fp013"},
                "meta_data": {"camera": "Fujifilm GFX 100S", "location": "Outdoor"}
            },
            {
                "url": "https://example.com/photo14.jpg",
                "title": "Portrait 6",
                "category": "Portrait",
                "id": "img_014",
                "event": {"year": "2024", "name": "Photo Contest", "short_name": "PC2024"},
                "user": {"name": "Leo Red", "email": "<EMAIL>", "honors": "AFIAP", "club": "Portrait Club", "fp_id": "fp014"},
                "meta_data": {"camera": "Nikon Z9", "location": "Studio"}
            },
            {
                "url": "https://example.com/photo15.jpg",
                "title": "Portrait 7",
                "category": "Portrait",
                "id": "img_015",
                "event": {"year": "2024", "name": "Photo Contest", "short_name": "PC2024"},
                "user": {"name": "Mia Yellow", "email": "<EMAIL>", "honors": "EFIAP", "club": "Portrait Club", "fp_id": "fp015"},
                "meta_data": {"camera": "Sony A1", "location": "Outdoor"}
            }
        ]
    }
]

def simulate_get_clusters(min_photos=None, max_photos=None, sort_by="total_photos", sort_order="desc", limit=None):
    """Simulate the GET /v1/clusters/ endpoint"""
    print("=== GET /v1/clusters/ ===")
    print(f"Parameters: min_photos={min_photos}, max_photos={max_photos}, sort_by={sort_by}, sort_order={sort_order}, limit={limit}")
    
    # Filter by min/max photos
    filtered_data = []
    for cluster in MOCK_CLUSTER_DATA:
        if min_photos is not None and cluster["total_images"] < min_photos:
            continue
        if max_photos is not None and cluster["total_images"] > max_photos:
            continue
        filtered_data.append(cluster)
    
    # Sort
    if sort_by == "total_photos":
        reverse = sort_order.lower() == "desc"
        filtered_data.sort(key=lambda x: x["total_images"], reverse=reverse)
    
    # Apply limit
    if limit is not None:
        filtered_data = filtered_data[:limit]
    
    response = {
        "message": "success",
        "result": filtered_data,
        "total_clusters": len(filtered_data),
        "error": False
    }
    
    print(f"Response: Found {len(filtered_data)} clusters")
    for cluster in filtered_data:
        print(f"  - Cluster {cluster['cluster_id']}: {cluster['total_images']} images")
    print()
    return response

def simulate_find_cluster_by_image(image_url):
    """Simulate the GET /v1/clusters/find-by-image endpoint"""
    print("=== GET /v1/clusters/find-by-image ===")
    print(f"Parameters: image_url={image_url}")
    
    # Find the cluster containing this image
    for cluster in MOCK_CLUSTER_DATA:
        for image in cluster["images"]:
            if image["url"] == image_url:
                response = {
                    "message": "success",
                    "cluster_id": cluster["cluster_id"],
                    "cluster_data": cluster,
                    "error": False
                }
                print(f"Response: Image found in cluster {cluster['cluster_id']} with {cluster['total_images']} images")
                print()
                return response
    
    # Image not found
    response = {
        "message": "Image not found or not assigned to any cluster",
        "cluster_id": None,
        "cluster_data": None,
        "error": False
    }
    print("Response: Image not found")
    print()
    return response

def main():
    """Demonstrate the clustering API functionality"""
    print("🔍 Clustering API Demo")
    print("=" * 50)
    
    # Test 1: Get all clusters
    print("Test 1: Get all clusters")
    simulate_get_clusters()
    
    # Test 2: Get clusters with at least 4 photos
    print("Test 2: Get clusters with at least 4 photos")
    simulate_get_clusters(min_photos=4)
    
    # Test 3: Get top 2 clusters by photo count
    print("Test 3: Get top 2 clusters by photo count")
    simulate_get_clusters(sort_by="total_photos", sort_order="desc", limit=2)
    
    # Test 4: Get clusters with 3-5 photos, sorted ascending
    print("Test 4: Get clusters with 3-5 photos, sorted ascending")
    simulate_get_clusters(min_photos=3, max_photos=5, sort_order="asc")
    
    # Test 5: Find cluster for a specific image
    print("Test 5: Find cluster for a specific image")
    simulate_find_cluster_by_image("https://example.com/photo1.jpg")
    
    # Test 6: Find cluster for non-existent image
    print("Test 6: Find cluster for non-existent image")
    simulate_find_cluster_by_image("https://example.com/nonexistent.jpg")
    
    print("✅ Demo completed!")

if __name__ == "__main__":
    main()
