#!/usr/bin/env bash

# URL to fetch
URL="https://api-similarity-dev-761230355208.asia-southeast1.run.app/"

# Record script start time (in seconds since epoch)
START_TS=$(date +%s)

# Iteration counter
ITER=0

# Function to print total elapsed time on exit
function finish {
  END_TS=$(date +%s)
  TOTAL=$(( END_TS - START_TS ))
  echo
  echo "=== Exiting ==="
  echo "Total runtime: ${TOTAL}s"
  exit 0
}

# Trap SIGINT (Ctrl+C) to run the finish function
trap finish SIGINT

echo "Starting loop at $(date). Hit Ctrl+C to stop."

while true; do
  ITER=$((ITER + 1))
  echo "----"
  echo "Iteration #$ITER  $(date '+%Y-%m-%d %H:%M:%S')"

  # Perform GET request, suppress output, but capture status & timing
  curl -s -o /dev/null \
       -w "HTTP status: %{http_code} | Request time: %{time_total}s\n" \
       "$URL"

  # Calculate elapsed time since script start
  NOW_TS=$(date +%s)
  ELAPSED=$(( NOW_TS - START_TS ))
  echo "Elapsed since start: ${ELAPSED}s"

  # Wait 20 minutes before next iteration
  sleep 60*20
done
