from typing import Optional, List, Dict, Any
from dataclasses import asdict
import numpy as np
import urllib.parse
from domain.repositories.similarity_repository import SimilarityRepository
from domain.entities.photo import Photo, Event, User
import json


class SimilarityRepoLocal(SimilarityRepository):
    def __init__(self):
        self.img_urls = np.load('model/sfi_url.npz')['urls']
        self.photos = self.load_photos()
        self.profiles = self.load_profiles()
        print('model and data loaded...')
        print(f'-- total imgage urls: {len(self.img_urls)}')
        print(f'-- total photos: {len(self.photos)}')


    def fetch_image_detail(self, url) -> Optional[Photo]:
        for photo in self.photos:
            if photo['url'] == url:
                user = self.find_user(photo['user_id'])
                if user is None:
                    print('can not find user with id: ', photo['user_id'])
                    return None

                event = Event(
                    year=photo['tahun'],
                    name=photo['event_name'],
                    short_name=photo['event_shortname']
                )

                user_data = User(
                    name=user['nama_lengkap'],
                    honors=user['gelar_fotografi'],
                    club=user['klub'],
                    fp_id=user['id'],
                    email=user['email']
                )

                photo_data = Photo(
                    title=photo['judul_foto'],
                    category=photo['kategori'],
                    event=event,
                    user=user_data,
                    meta_data=photo.get('meta_data')
                )
                return photo_data

        return None

    def fetch_image_by_index(self, index) -> Optional[str]:
        return self.img_urls[index]

    def find_user(self, id):
        for user in self.profiles:
            if user['id'] == id:
                return user
        return None

    def load_photos(self):
        with open('model/photos.json') as f:
            photos = json.load(f)

        result = []
        for photo in photos:
            if 'results' not in photo: continue                
            for img in photo['results']:
                if 'url' not in img:
                    img['url'] = f'https://simfoni.fpsi.or.id/data/accepted_photo/{img["folder"]}/{urllib.parse.quote(img["filename"])}'                
                img['user_id'] = photo['query']['id']
                result.append(img)
        return result


    def load_profiles(self):
        with open('model/profiles.json') as f:
            profiles = json.load(f)

        result = []
        for profile in profiles:
            if 'results' not in profile: continue     
            result.append(profile['results'])           
        return result

    def fetch_clusters(self, min_photos: Optional[int] = None, max_photos: Optional[int] = None,
                      sort_by: str = "total_photos", sort_order: str = "desc",
                      limit: Optional[int] = None) -> List[Dict[str, Any]]:
        """
        Fetch cluster data grouped by cluster_id for local implementation.
        Note: This is a basic implementation for local testing.
        """
        # Group photos by cluster_id
        clusters = {}
        for photo in self.photos:
            cluster_id = photo.get('cluster_id', 0)
            if cluster_id not in clusters:
                clusters[cluster_id] = []
            clusters[cluster_id].append(photo)

        # Convert to result format
        result = []
        for cluster_id, images in clusters.items():
            # Filter by min/max photos
            if min_photos is not None and len(images) < min_photos:
                continue
            if max_photos is not None and len(images) > max_photos:
                continue

            cluster_data = {
                "cluster_id": cluster_id,
                "total_images": len(images),
                "images": []
            }

            for image in images:
                image_data = {
                    "url": image["url"],
                    "title": image.get("judul_foto"),
                    "category": image.get("kategori"),
                    "id": image.get("id"),
                    "meta_data": image.get("meta_data")
                }
                cluster_data["images"].append(image_data)

            result.append(cluster_data)

        # Sort results
        if sort_by == "total_photos":
            reverse = sort_order.lower() == "desc"
            result.sort(key=lambda x: x["total_images"], reverse=reverse)

        # Apply limit
        if limit is not None:
            result = result[:limit]

        return result

    def find_image_cluster(self, image_url: str) -> Optional[Dict[str, Any]]:
        """
        Find which cluster an image belongs to for local implementation.
        """
        # Find the image
        target_photo = None
        for photo in self.photos:
            if photo['url'] == image_url:
                target_photo = photo
                break

        if not target_photo or 'cluster_id' not in target_photo:
            return None

        cluster_id = target_photo['cluster_id']

        # Get all images in the same cluster
        cluster_images = [photo for photo in self.photos if photo.get('cluster_id') == cluster_id]

        cluster_data = {
            "cluster_id": cluster_id,
            "total_images": len(cluster_images),
            "images": []
        }

        for image in cluster_images:
            image_data = {
                "url": image["url"],
                "title": image.get("judul_foto"),
                "category": image.get("kategori"),
                "id": image.get("id"),
                "meta_data": image.get("meta_data")
            }
            cluster_data["images"].append(image_data)

        return cluster_data

    async def batch_insert_photos(self, photos: List[Dict[str, Any]]) -> Dict[str, Any]:
        """
        Batch insert photos for local implementation.
        Note: This is a basic implementation for local testing.
        """
        successful = 0
        failed = 0
        failed_details = []

        for photo in photos:
            try:
                # Basic validation
                if 'url' not in photo:
                    raise ValueError("Missing required field: url")

                # Add to local storage (in memory)
                self.photos.append(photo)
                successful += 1

            except Exception as e:
                failed += 1
                failed_details.append({
                    'photo_id': photo.get('id', 'unknown'),
                    'error': str(e)
                })

        return {
            'status': 'success' if failed == 0 else 'partial',
            'message': 'All photos inserted successfully' if failed == 0 else 'Some photos failed to insert',
            'details': {
                'total_processed': len(photos),
                'successful': successful,
                'failed': failed,
                'failed_details': failed_details
            }
        }

    async def batch_insert_photos(self, photos: List[Dict[str, Any]]) -> Dict[str, Any]:
        """
        Insert multiple photos in a batch operation.
        
        Args:
            photos: List of photo data dictionaries to insert
            
        Returns:
            Dict containing operation results and statistics
        """
        successful = 0
        failed = 0
        failed_details = []
        
        try:
            # Load existing photos
            existing_photos = self.load_photos()
            
            # Process each photo
            for photo in photos:
                try:
                    # Validate required fields
                    required_fields = ['id', 'url', 'features']
                    if not all(field in photo for field in required_fields):
                        raise ValueError(f"Missing required fields: {required_fields}")
                    
                    # Check if photo already exists
                    if any(p['url'] == photo['url'] for p in existing_photos):
                        raise ValueError("Photo with this URL already exists")
                    
                    # Add to existing photos
                    existing_photos.append(photo)
                    successful += 1
                    
                except Exception as e:
                    failed += 1
                    failed_details.append({
                        'photo_id': photo.get('id', 'unknown'),
                        'error': str(e)
                    })
                    continue
            
            # Save updated photos back to file
            if successful > 0:
                with open('model/photos.json', 'w') as f:
                    json.dump(existing_photos, f, indent=2)
            
            return {
                'status': 'success' if failed == 0 else 'partial',
                'message': 'All photos inserted successfully' if failed == 0 else 'Some photos failed to insert',
                'details': {
                    'total_processed': len(photos),
                    'successful': successful,
                    'failed': failed,
                    'failed_details': failed_details
                }
            }
            
        except Exception as e:
            return {
                'status': 'error',
                'message': f'Batch insert failed: {str(e)}',
                'details': {
                    'total_processed': len(photos),
                    'successful': successful,
                    'failed': failed,
                    'failed_details': failed_details
                }
            }
